#!/usr/bin/env tsx

// 基金API接口测试脚本

import { apiTester, testFundApi } from '../src/lib/api/apiTester';

/**
 * 测试多个基金代码
 */
async function testMultipleFunds() {
  const fundCodes = ['000628', '110022', '161725', '000001', '519736'];
  
  console.log('🚀 开始批量测试基金API接口...\n');
  
  for (const fundCode of fundCodes) {
    try {
      console.log(`📊 测试基金 ${fundCode}...`);
      const result = await testFundApi(fundCode);
      const report = apiTester.generateReport(result);
      console.log(report);
      
      // 等待一秒避免请求过于频繁
      await new Promise(resolve => setTimeout(resolve, 1000));
      
    } catch (error) {
      console.error(`❌ 测试基金 ${fundCode} 失败:`, error);
    }
  }
}

/**
 * 测试不同参数组合
 */
async function testParameterCombinations() {
  const fundCode = '000628';
  
  console.log(`🔬 测试基金 ${fundCode} 的不同参数组合...\n`);
  
  const testCases = [
    { name: '净值数据-1个月', params: { fundCode, dataType: 'nvl' as const, months: 1 } },
    { name: '净值数据-12个月', params: { fundCode, dataType: 'nvl' as const, months: 12 } },
    { name: '净值数据-36个月', params: { fundCode, dataType: 'nvl' as const, months: 36 } },
    { name: '净值数据-124个月', params: { fundCode, dataType: 'nvl' as const, months: 124 } },
    { name: '业绩对比-3个月', params: { fundCode, dataType: 'ai' as const, months: 3, source: 'qieman' } },
    { name: '业绩对比-6个月', params: { fundCode, dataType: 'ai' as const, months: 6, source: 'qieman' } },
    { name: '业绩对比-12个月', params: { fundCode, dataType: 'ai' as const, months: 12, source: 'qieman' } },
  ];
  
  for (const testCase of testCases) {
    try {
      console.log(`🧪 ${testCase.name}...`);
      const result = await apiTester.testSingleApi(testCase.params);
      
      if (result.success) {
        console.log(`  ✅ 成功 - ${result.dataPoints}个数据点 (${result.responseTime}ms)`);
        if (result.sampleData) {
          console.log(`  📝 示例数据:`, JSON.stringify(result.sampleData, null, 2).substring(0, 200) + '...');
        }
      } else {
        console.log(`  ❌ 失败 - ${result.error}`);
      }
      
      console.log(`  🔗 URL: ${result.url}\n`);
      
      // 等待500ms避免请求过于频繁
      await new Promise(resolve => setTimeout(resolve, 500));
      
    } catch (error) {
      console.error(`❌ 测试 ${testCase.name} 失败:`, error);
    }
  }
}

/**
 * 测试错误情况
 */
async function testErrorCases() {
  console.log('🚨 测试错误情况...\n');
  
  const errorCases = [
    { name: '无效基金代码', params: { fundCode: '123456', dataType: 'nvl' as const } },
    { name: '不存在的基金', params: { fundCode: '999999', dataType: 'nvl' as const } },
    { name: '无效月数参数', params: { fundCode: '000628', dataType: 'nvl' as const, months: 999 } },
    { name: 'ai类型缺少source', params: { fundCode: '000628', dataType: 'ai' as const, months: 3 } },
  ];
  
  for (const errorCase of errorCases) {
    try {
      console.log(`🧪 ${errorCase.name}...`);
      const result = await apiTester.testSingleApi(errorCase.params);
      
      if (result.success) {
        console.log(`  ⚠️  意外成功 - 应该失败但成功了`);
      } else {
        console.log(`  ✅ 预期失败 - ${result.error}`);
      }
      
    } catch (error) {
      console.log(`  ✅ 预期异常 - ${error}`);
    }
  }
}

/**
 * 性能测试
 */
async function performanceTest() {
  console.log('⚡ 性能测试...\n');
  
  const fundCode = '000628';
  const testCount = 5;
  const results: number[] = [];
  
  for (let i = 0; i < testCount; i++) {
    console.log(`🏃 第 ${i + 1}/${testCount} 次测试...`);
    
    const startTime = Date.now();
    const result = await apiTester.testSingleApi({
      fundCode,
      dataType: 'nvl',
      months: 12
    });
    const endTime = Date.now();
    
    if (result.success) {
      results.push(endTime - startTime);
      console.log(`  ✅ 成功 - ${endTime - startTime}ms`);
    } else {
      console.log(`  ❌ 失败 - ${result.error}`);
    }
    
    // 等待1秒
    await new Promise(resolve => setTimeout(resolve, 1000));
  }
  
  if (results.length > 0) {
    const avgTime = results.reduce((sum, time) => sum + time, 0) / results.length;
    const minTime = Math.min(...results);
    const maxTime = Math.max(...results);
    
    console.log(`\n📊 性能统计:`);
    console.log(`  平均响应时间: ${avgTime.toFixed(0)}ms`);
    console.log(`  最快响应时间: ${minTime}ms`);
    console.log(`  最慢响应时间: ${maxTime}ms`);
    console.log(`  成功率: ${(results.length / testCount * 100).toFixed(1)}%`);
  }
}

/**
 * 主函数
 */
async function main() {
  console.log('🎯 基金API接口全面测试\n');
  console.log('='.repeat(50) + '\n');

  try {
    // 设置超时时间
    apiTester.setTimeout(15000);

    // 1. 测试参数组合
    await testParameterCombinations();
    console.log('\n' + '='.repeat(50) + '\n');

    // 2. 测试错误情况
    await testErrorCases();
    console.log('\n' + '='.repeat(50) + '\n');

    // 3. 性能测试
    await performanceTest();
    console.log('\n' + '='.repeat(50) + '\n');

    // 4. 批量测试多个基金
    await testMultipleFunds();

    console.log('🎉 所有测试完成！');

  } catch (error) {
    console.error('💥 测试过程中发生错误:', error);
    process.exit(1);
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  main().catch(console.error);
}

export { main as runApiTests };
