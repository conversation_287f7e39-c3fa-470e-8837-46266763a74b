// 基金API使用示例

import { 
  fetchFundData, 
  getPerformanceComparison, 
  validateFund,
  getFundBasicInfo 
} from '../src/lib/api/fundApi';
import { apiTester } from '../src/lib/api/apiTester';

/**
 * 示例1: 获取基金基础数据
 */
async function example1_BasicFundData() {
  console.log('=== 示例1: 获取基金基础数据 ===');
  
  const fundCode = '000628';
  
  try {
    // 验证基金代码
    const isValid = await validateFund(fundCode);
    console.log(`基金 ${fundCode} 是否有效:`, isValid);
    
    // 获取基金基本信息
    const info = await getFundBasicInfo(fundCode);
    console.log('基金信息:', info);
    
    // 获取最近12个月数据
    const data = await fetchFundData(fundCode);
    console.log(`获取到 ${data.length} 个数据点`);
    console.log('最新数据:', data[data.length - 1]);
    console.log('最早数据:', data[0]);
    
  } catch (error) {
    console.error('获取基金数据失败:', error);
  }
}

/**
 * 示例2: 获取指定时间范围的数据
 */
async function example2_DateRangeData() {
  console.log('\n=== 示例2: 获取指定时间范围的数据 ===');
  
  const fundCode = '000628';
  const startDate = '2024-01-01';
  const endDate = '2024-12-31';
  
  try {
    const data = await fetchFundData(fundCode, startDate, endDate);
    console.log(`${startDate} 到 ${endDate} 期间数据点数: ${data.length}`);
    
    // 计算期间收益率
    const startValue = data[0].netAssetValue;
    const endValue = data[data.length - 1].netAssetValue;
    const returnRate = ((endValue - startValue) / startValue * 100).toFixed(2);
    
    console.log(`期间收益率: ${returnRate}%`);
    console.log(`起始净值: ${startValue}`);
    console.log(`结束净值: ${endValue}`);
    
  } catch (error) {
    console.error('获取时间范围数据失败:', error);
  }
}

/**
 * 示例3: 获取业绩对比数据
 */
async function example3_PerformanceComparison() {
  console.log('\n=== 示例3: 获取业绩对比数据 ===');
  
  const fundCode = '000628';
  
  try {
    // 获取3个月业绩对比
    const comparison = await getPerformanceComparison(fundCode, 3);
    
    console.log('业绩对比数据:');
    console.log(`- 本基金数据点: ${comparison.fund.length}`);
    console.log(`- 同类平均数据点: ${comparison.benchmark.length}`);
    console.log(`- 指数数据点: ${comparison.index.length}`);
    
    // 计算最终收益率
    const fundReturn = comparison.fund[comparison.fund.length - 1]?.return || 0;
    const benchmarkReturn = comparison.benchmark[comparison.benchmark.length - 1]?.return || 0;
    const indexReturn = comparison.index[comparison.index.length - 1]?.return || 0;
    
    console.log('\n3个月收益率对比:');
    console.log(`- 本基金: ${fundReturn.toFixed(2)}%`);
    console.log(`- 同类平均: ${benchmarkReturn.toFixed(2)}%`);
    console.log(`- 沪深300: ${indexReturn.toFixed(2)}%`);
    
    // 计算超额收益
    const excessReturn = fundReturn - benchmarkReturn;
    console.log(`- 超额收益: ${excessReturn.toFixed(2)}%`);
    
  } catch (error) {
    console.error('获取业绩对比数据失败:', error);
  }
}

/**
 * 示例4: 批量获取多个基金数据
 */
async function example4_MultipleFunds() {
  console.log('\n=== 示例4: 批量获取多个基金数据 ===');
  
  const fundCodes = ['000628', '110022', '161725'];
  
  try {
    console.log('开始批量获取基金数据...');
    
    const results = await Promise.all(
      fundCodes.map(async (code) => {
        try {
          const data = await fetchFundData(code);
          const latestData = data[data.length - 1];
          
          return {
            code,
            success: true,
            dataPoints: data.length,
            latestValue: latestData.netAssetValue,
            latestDate: latestData.date
          };
        } catch (error) {
          return {
            code,
            success: false,
            error: error instanceof Error ? error.message : '未知错误'
          };
        }
      })
    );
    
    console.log('\n批量获取结果:');
    results.forEach(result => {
      if (result.success) {
        console.log(`✅ ${result.code}: ${result.dataPoints}个数据点, 最新净值: ${result.latestValue} (${result.latestDate})`);
      } else {
        console.log(`❌ ${result.code}: ${result.error}`);
      }
    });
    
  } catch (error) {
    console.error('批量获取失败:', error);
  }
}

/**
 * 示例5: 使用缓存优化性能
 */
async function example5_CacheOptimization() {
  console.log('\n=== 示例5: 使用缓存优化性能 ===');
  
  const fundCode = '000628';
  
  try {
    console.log('第一次请求 (无缓存):');
    const start1 = Date.now();
    const data1 = await fetchFundData(fundCode, undefined, undefined, { useCache: true });
    const time1 = Date.now() - start1;
    console.log(`耗时: ${time1}ms, 数据点: ${data1.length}`);
    
    console.log('\n第二次请求 (使用缓存):');
    const start2 = Date.now();
    const data2 = await fetchFundData(fundCode, undefined, undefined, { useCache: true });
    const time2 = Date.now() - start2;
    console.log(`耗时: ${time2}ms, 数据点: ${data2.length}`);
    
    console.log(`\n缓存效果: 速度提升 ${((time1 - time2) / time1 * 100).toFixed(1)}%`);
    
  } catch (error) {
    console.error('缓存测试失败:', error);
  }
}

/**
 * 示例6: 错误处理和重试机制
 */
async function example6_ErrorHandling() {
  console.log('\n=== 示例6: 错误处理和重试机制 ===');
  
  try {
    // 测试无效基金代码
    console.log('测试无效基金代码...');
    const invalidResult = await apiTester.testSingleApi({
      fundCode: '999999',
      dataType: 'nvl'
    });
    
    console.log(`结果: ${invalidResult.success ? '成功' : '失败'}`);
    if (!invalidResult.success) {
      console.log(`错误信息: ${invalidResult.error}`);
    }
    
    // 测试网络重试
    console.log('\n测试重试机制...');
    const data = await fetchFundData('000628', undefined, undefined, {
      retryCount: 3,
      retryDelay: 1000,
      timeout: 5000
    });
    
    console.log(`重试机制测试成功，获取到 ${data.length} 个数据点`);
    
  } catch (error) {
    console.error('错误处理测试:', error);
  }
}

/**
 * 示例7: 数据分析和计算
 */
async function example7_DataAnalysis() {
  console.log('\n=== 示例7: 数据分析和计算 ===');
  
  const fundCode = '000628';
  
  try {
    const data = await fetchFundData(fundCode);
    
    // 计算统计指标
    const values = data.map(d => d.netAssetValue);
    const returns = [];
    
    for (let i = 1; i < values.length; i++) {
      const dailyReturn = (values[i] - values[i-1]) / values[i-1];
      returns.push(dailyReturn);
    }
    
    // 基础统计
    const totalReturn = (values[values.length - 1] - values[0]) / values[0];
    const avgDailyReturn = returns.reduce((sum, r) => sum + r, 0) / returns.length;
    const volatility = Math.sqrt(
      returns.reduce((sum, r) => sum + Math.pow(r - avgDailyReturn, 2), 0) / returns.length
    );
    
    // 最大回撤
    let maxDrawdown = 0;
    let peak = values[0];
    
    for (const value of values) {
      if (value > peak) {
        peak = value;
      } else {
        const drawdown = (peak - value) / peak;
        maxDrawdown = Math.max(maxDrawdown, drawdown);
      }
    }
    
    console.log('数据分析结果:');
    console.log(`- 数据期间: ${data[0].date} 至 ${data[data.length - 1].date}`);
    console.log(`- 总收益率: ${(totalReturn * 100).toFixed(2)}%`);
    console.log(`- 年化收益率: ${(totalReturn * 365 / data.length * 100).toFixed(2)}%`);
    console.log(`- 日均收益率: ${(avgDailyReturn * 100).toFixed(4)}%`);
    console.log(`- 波动率: ${(volatility * 100).toFixed(2)}%`);
    console.log(`- 最大回撤: ${(maxDrawdown * 100).toFixed(2)}%`);
    
  } catch (error) {
    console.error('数据分析失败:', error);
  }
}

/**
 * 运行所有示例
 */
async function runAllExamples() {
  console.log('🚀 基金API使用示例演示\n');
  
  await example1_BasicFundData();
  await example2_DateRangeData();
  await example3_PerformanceComparison();
  await example4_MultipleFunds();
  await example5_CacheOptimization();
  await example6_ErrorHandling();
  await example7_DataAnalysis();
  
  console.log('\n🎉 所有示例运行完成！');
}

// 如果直接运行此文件
if (require.main === module) {
  runAllExamples().catch(console.error);
}

export {
  example1_BasicFundData,
  example2_DateRangeData,
  example3_PerformanceComparison,
  example4_MultipleFunds,
  example5_CacheOptimization,
  example6_ErrorHandling,
  example7_DataAnalysis,
  runAllExamples
};
