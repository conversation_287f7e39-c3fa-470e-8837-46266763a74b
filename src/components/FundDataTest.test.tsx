import { describe, it, expect, vi, beforeEach } from 'vitest';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import FundDataTest from '@/components/FundDataTest';
import { fetchFundData, getFundBasicInfo } from '@/lib/api/fundApi';

// Mock API functions
vi.mock('@/lib/api/fundApi');

const mockFundData = [
  { date: '2024-01-01', netAssetValue: 1.0000, accumulatedValue: 1.0000 },
  { date: '2024-01-02', netAssetValue: 1.0100, accumulatedValue: 1.0100 },
  { date: '2024-01-03', netAssetValue: 1.0050, accumulatedValue: 1.0050 },
];

const mockFundInfo = {
  code: '000628',
  name: '大成高新技术产业股票',
};

describe('FundDataTest', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    (fetchFundData as any).mockResolvedValue(mockFundData);
    (getFundBasicInfo as any).mockResolvedValue(mockFundInfo);
  });

  it('应该渲染基本组件', () => {
    render(<FundDataTest />);

    expect(screen.getByText('基金数据获取测试')).toBeInTheDocument();
    expect(screen.getByLabelText('基金代码')).toBeInTheDocument();
    expect(screen.getByLabelText('开始日期')).toBeInTheDocument();
    expect(screen.getByLabelText('结束日期')).toBeInTheDocument();
    expect(screen.getByText('获取数据')).toBeInTheDocument();
  });

  it('应该有默认值', () => {
    render(<FundDataTest />);

    expect(screen.getByDisplayValue('000628')).toBeInTheDocument();
    expect(screen.getByDisplayValue('2024-01-01')).toBeInTheDocument();
    expect(screen.getByDisplayValue('2024-12-31')).toBeInTheDocument();
  });

  it('应该允许修改输入值', async () => {
    const user = userEvent.setup();
    render(<FundDataTest />);

    const fundCodeInput = screen.getByLabelText('基金代码');
    const startDateInput = screen.getByLabelText('开始日期');
    const endDateInput = screen.getByLabelText('结束日期');

    await user.clear(fundCodeInput);
    await user.type(fundCodeInput, '110022');
    expect(screen.getByDisplayValue('110022')).toBeInTheDocument();

    await user.clear(startDateInput);
    await user.type(startDateInput, '2023-01-01');
    expect(screen.getByDisplayValue('2023-01-01')).toBeInTheDocument();

    await user.clear(endDateInput);
    await user.type(endDateInput, '2023-12-31');
    expect(screen.getByDisplayValue('2023-12-31')).toBeInTheDocument();
  });

  it('应该成功获取基金数据', async () => {
    const user = userEvent.setup();
    render(<FundDataTest />);

    const getDataButton = screen.getByText('获取数据');
    await user.click(getDataButton);

    await waitFor(() => {
      expect(fetchFundData).toHaveBeenCalledWith('000628', '2024-01-01', '2024-12-31');
      expect(getFundBasicInfo).toHaveBeenCalledWith('000628');
    });

    // 验证基金信息显示
    await waitFor(() => {
      expect(screen.getByText('基金信息')).toBeInTheDocument();
      expect(screen.getByText('000628')).toBeInTheDocument();
      expect(screen.getByText('大成高新技术产业股票')).toBeInTheDocument();
    });

    // 验证数据统计显示
    await waitFor(() => {
      expect(screen.getByText('数据统计')).toBeInTheDocument();
      expect(screen.getByText('3')).toBeInTheDocument(); // 数据条数
      expect(screen.getByText('1.0050')).toBeInTheDocument(); // 最新净值
      expect(screen.getByText('2024-01-03')).toBeInTheDocument(); // 最新日期
    });

    // 验证数据表格显示
    await waitFor(() => {
      expect(screen.getByText('历史净值数据 (最近10条)')).toBeInTheDocument();
      expect(screen.getByText('日期')).toBeInTheDocument();
      expect(screen.getByText('单位净值')).toBeInTheDocument();
      expect(screen.getByText('累计净值')).toBeInTheDocument();
    });
  });

  it('应该显示加载状态', async () => {
    const user = userEvent.setup();
    // 让API调用延迟返回
    (fetchFundData as any).mockImplementation(() => new Promise(resolve => setTimeout(resolve, 100)));

    render(<FundDataTest />);

    const getDataButton = screen.getByText('获取数据');
    await user.click(getDataButton);

    // 应该显示加载状态
    expect(screen.getByText('获取中...')).toBeInTheDocument();
    expect(getDataButton).toBeDisabled();
  });

  it('应该处理API错误', async () => {
    const user = userEvent.setup();
    const errorMessage = '基金代码不存在';
    (fetchFundData as any).mockRejectedValue(new Error(errorMessage));

    render(<FundDataTest />);

    const getDataButton = screen.getByText('获取数据');
    await user.click(getDataButton);

    await waitFor(() => {
      expect(screen.getByText('错误信息')).toBeInTheDocument();
      expect(screen.getByText(errorMessage)).toBeInTheDocument();
    });
  });

  it('应该处理基金信息获取失败', async () => {
    const user = userEvent.setup();
    (getFundBasicInfo as any).mockRejectedValue(new Error('获取基金信息失败'));

    render(<FundDataTest />);

    const getDataButton = screen.getByText('获取数据');
    await user.click(getDataButton);

    await waitFor(() => {
      expect(screen.getByText('错误信息')).toBeInTheDocument();
      expect(screen.getByText('获取基金信息失败')).toBeInTheDocument();
    });
  });

  it('应该禁用空基金代码的提交', () => {
    render(<FundDataTest />);

    const fundCodeInput = screen.getByLabelText('基金代码');
    fireEvent.change(fundCodeInput, { target: { value: '' } });

    const getDataButton = screen.getByText('获取数据');
    expect(getDataButton).toBeDisabled();
  });

  it('应该正确显示数据表格', async () => {
    const user = userEvent.setup();
    render(<FundDataTest />);

    const getDataButton = screen.getByText('获取数据');
    await user.click(getDataButton);

    await waitFor(() => {
      // 验证表格数据（应该按日期倒序显示）
      const rows = screen.getAllByRole('row');
      expect(rows).toHaveLength(4); // 1个表头 + 3条数据

      // 验证最新数据在第一行
      expect(rows[1]).toHaveTextContent('2024-01-03');
      expect(rows[1]).toHaveTextContent('1.0050');
    });
  });

  it('应该处理没有累计净值的数据', async () => {
    const user = userEvent.setup();
    const dataWithoutAccumulated = [
      { date: '2024-01-01', netAssetValue: 1.0000 },
    ];
    (fetchFundData as any).mockResolvedValue(dataWithoutAccumulated);

    render(<FundDataTest />);

    const getDataButton = screen.getByText('获取数据');
    await user.click(getDataButton);

    await waitFor(() => {
      expect(screen.getByText('N/A')).toBeInTheDocument();
    });
  });

  it('应该处理空数据响应', async () => {
    const user = userEvent.setup();
    (fetchFundData as any).mockResolvedValue([]);

    render(<FundDataTest />);

    const getDataButton = screen.getByText('获取数据');
    await user.click(getDataButton);

    await waitFor(() => {
      // 不应该显示数据统计和表格
      expect(screen.queryByText('数据统计')).not.toBeInTheDocument();
      expect(screen.queryByText('历史净值数据')).not.toBeInTheDocument();
    });
  });

  it('应该清除之前的数据和错误', async () => {
    const user = userEvent.setup();
    
    // 首先触发一个错误
    (fetchFundData as any).mockRejectedValue(new Error('测试错误'));
    render(<FundDataTest />);

    let getDataButton = screen.getByText('获取数据');
    await user.click(getDataButton);

    await waitFor(() => {
      expect(screen.getByText('测试错误')).toBeInTheDocument();
    });

    // 然后成功获取数据
    (fetchFundData as any).mockResolvedValue(mockFundData);
    getDataButton = screen.getByText('获取数据');
    await user.click(getDataButton);

    await waitFor(() => {
      // 错误信息应该被清除
      expect(screen.queryByText('测试错误')).not.toBeInTheDocument();
      // 应该显示新的数据
      expect(screen.getByText('数据统计')).toBeInTheDocument();
    });
  });

  it('应该正确格式化数值显示', async () => {
    const user = userEvent.setup();
    const preciseData = [
      { date: '2024-01-01', netAssetValue: 1.23456789, accumulatedValue: 1.98765432 },
    ];
    (fetchFundData as any).mockResolvedValue(preciseData);

    render(<FundDataTest />);

    const getDataButton = screen.getByText('获取数据');
    await user.click(getDataButton);

    await waitFor(() => {
      // 验证数值格式化为4位小数
      expect(screen.getByText('1.2346')).toBeInTheDocument();
      expect(screen.getByText('1.9877')).toBeInTheDocument();
    });
  });
});
