'use client';

import { useState } from 'react';
import { fetchFundData, getFundBasicInfo } from '@/lib/api/fundApi';
import type { FundData } from '@/types/fund';

export default function FundDataTest() {
  const [fundCode, setFundCode] = useState('000628');
  const [startDate, setStartDate] = useState('2024-01-01');
  const [endDate, setEndDate] = useState('2024-12-31');
  const [loading, setLoading] = useState(false);
  const [data, setData] = useState<FundData[]>([]);
  const [error, setError] = useState<string>('');
  const [fundInfo, setFundInfo] = useState<any>(null);

  const handleTest = async () => {
    setLoading(true);
    setError('');
    setData([]);
    setFundInfo(null);

    try {
      console.log(`开始获取基金${fundCode}的数据...`);
      
      // 获取基金基本信息
      const info = await getFundBasicInfo(fundCode);
      setFundInfo(info);
      
      // 获取基金历史数据
      const fundData = await fetchFundData(fundCode, startDate, endDate);
      setData(fundData);
      
      console.log(`成功获取${fundData.length}条数据`);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : '未知错误';
      setError(errorMessage);
      console.error('获取基金数据失败:', err);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="max-w-4xl mx-auto p-6 space-y-6">
      <div className="bg-white rounded-lg shadow-md p-6">
        <h2 className="text-2xl font-bold text-gray-900 mb-6">基金数据获取测试</h2>
        
        {/* 输入表单 */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              基金代码
            </label>
            <input
              type="text"
              value={fundCode}
              onChange={(e) => setFundCode(e.target.value)}
              placeholder="例如: 000628"
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              开始日期
            </label>
            <input
              type="date"
              value={startDate}
              onChange={(e) => setStartDate(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              结束日期
            </label>
            <input
              type="date"
              value={endDate}
              onChange={(e) => setEndDate(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>
          
          <div className="flex items-end">
            <button
              onClick={handleTest}
              disabled={loading || !fundCode}
              className="w-full px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:bg-gray-400 disabled:cursor-not-allowed"
            >
              {loading ? '获取中...' : '获取数据'}
            </button>
          </div>
        </div>

        {/* 基金信息 */}
        {fundInfo && (
          <div className="mb-6 p-4 bg-blue-50 border border-blue-200 rounded-md">
            <h3 className="font-medium text-blue-900 mb-2">基金信息</h3>
            <div className="text-sm text-blue-800">
              <p><span className="font-medium">代码:</span> {fundInfo.code}</p>
              <p><span className="font-medium">名称:</span> {fundInfo.name}</p>
            </div>
          </div>
        )}

        {/* 错误信息 */}
        {error && (
          <div className="mb-6 p-4 bg-red-50 border border-red-200 rounded-md">
            <h3 className="font-medium text-red-900 mb-2">错误信息</h3>
            <p className="text-sm text-red-800">{error}</p>
          </div>
        )}

        {/* 数据统计 */}
        {data.length > 0 && (
          <div className="mb-6 p-4 bg-green-50 border border-green-200 rounded-md">
            <h3 className="font-medium text-green-900 mb-2">数据统计</h3>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm text-green-800">
              <div>
                <span className="font-medium">数据条数:</span> {data.length}
              </div>
              <div>
                <span className="font-medium">最新净值:</span> {data[data.length - 1]?.netAssetValue.toFixed(4)}
              </div>
              <div>
                <span className="font-medium">最新日期:</span> {data[data.length - 1]?.date}
              </div>
              <div>
                <span className="font-medium">累计净值:</span> {data[data.length - 1]?.accumulatedValue?.toFixed(4) || 'N/A'}
              </div>
            </div>
          </div>
        )}

        {/* 数据表格 */}
        {data.length > 0 && (
          <div className="overflow-x-auto">
            <h3 className="font-medium text-gray-900 mb-4">历史净值数据 (最近10条)</h3>
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    日期
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    单位净值
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    累计净值
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {data.slice(-10).reverse().map((item, index) => (
                  <tr key={index} className={index % 2 === 0 ? 'bg-white' : 'bg-gray-50'}>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {item.date}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {item.netAssetValue.toFixed(4)}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {item.accumulatedValue?.toFixed(4) || 'N/A'}
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        )}
      </div>
    </div>
  );
}
