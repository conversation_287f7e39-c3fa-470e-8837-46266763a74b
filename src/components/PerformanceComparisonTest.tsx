'use client';

import { useState } from 'react';
import PerformanceComparisonChart from './PerformanceComparisonChart';
import type { BacktestResult } from '@/types/fund';

// 模拟回测结果数据用于测试
const mockBacktestResult: BacktestResult = {
  strategy: 'fixed_amount',
  fund: {
    id: 'test-fund',
    name: '测试基金',
    code: '000001',
    type: 'equity',
    riskLevel: 'medium',
    indexId: 'test-index'
  },
  params: {
    startDate: '2023-01-01',
    endDate: '2024-01-01',
    initialAmount: 50000
  },
  performance: {
    totalReturn: 15.5,
    annualizedReturn: 15.5,
    volatility: 12.3,
    sharpeRatio: 1.26,
    maxDrawdown: 8.2,
    totalInvestment: 50000,
    finalValue: 57750
  },
  timeline: [
    {
      date: '2023-01-01',
      investment: 5000,
      totalInvestment: 5000,
      shares: 5000,
      value: 5000,
      return: 0,
      netAssetValue: 1.0
    },
    {
      date: '2023-02-01',
      investment: 5000,
      totalInvestment: 10000,
      shares: 9950,
      value: 10450,
      return: 4.5,
      netAssetValue: 1.05
    },
    {
      date: '2023-03-01',
      investment: 5000,
      totalInvestment: 15000,
      shares: 14762,
      value: 15900,
      return: 6.0,
      netAssetValue: 1.077
    },
    {
      date: '2023-04-01',
      investment: 5000,
      totalInvestment: 20000,
      shares: 19512,
      value: 19512,
      return: -2.44,
      netAssetValue: 1.0
    },
    {
      date: '2023-05-01',
      investment: 5000,
      totalInvestment: 25000,
      shares: 24390,
      value: 26829,
      return: 7.32,
      netAssetValue: 1.1
    },
    {
      date: '2023-06-01',
      investment: 5000,
      totalInvestment: 30000,
      shares: 28846,
      value: 31731,
      return: 5.77,
      netAssetValue: 1.1
    },
    {
      date: '2023-07-01',
      investment: 5000,
      totalInvestment: 35000,
      shares: 33333,
      value: 36666,
      return: 4.76,
      netAssetValue: 1.1
    },
    {
      date: '2023-08-01',
      investment: 5000,
      totalInvestment: 40000,
      shares: 37879,
      value: 41667,
      return: 4.17,
      netAssetValue: 1.1
    },
    {
      date: '2023-09-01',
      investment: 5000,
      totalInvestment: 45000,
      shares: 42424,
      value: 46667,
      return: 3.70,
      netAssetValue: 1.1
    },
    {
      date: '2023-10-01',
      investment: 5000,
      totalInvestment: 50000,
      shares: 46970,
      value: 51667,
      return: 3.33,
      netAssetValue: 1.1
    },
    {
      date: '2023-11-01',
      investment: 0,
      totalInvestment: 50000,
      shares: 46970,
      value: 54267,
      return: 8.53,
      netAssetValue: 1.155
    },
    {
      date: '2023-12-01',
      investment: 0,
      totalInvestment: 50000,
      shares: 46970,
      value: 57750,
      return: 15.5,
      netAssetValue: 1.229
    }
  ],
  performanceComparison: [
    { date: '2023-01-01', strategyReturn: 0, fundReturn: 0, indexReturn: 0 },
    { date: '2023-02-01', strategyReturn: 4.5, fundReturn: 5.0, indexReturn: 3.2 },
    { date: '2023-03-01', strategyReturn: 6.0, fundReturn: 7.7, indexReturn: 5.1 },
    { date: '2023-04-01', strategyReturn: -2.44, fundReturn: 0, indexReturn: -1.5 },
    { date: '2023-05-01', strategyReturn: 7.32, fundReturn: 10.0, indexReturn: 8.3 },
    { date: '2023-06-01', strategyReturn: 5.77, fundReturn: 10.0, indexReturn: 7.8 },
    { date: '2023-07-01', strategyReturn: 4.76, fundReturn: 10.0, indexReturn: 6.9 },
    { date: '2023-08-01', strategyReturn: 4.17, fundReturn: 10.0, indexReturn: 7.2 },
    { date: '2023-09-01', strategyReturn: 3.70, fundReturn: 10.0, indexReturn: 8.1 },
    { date: '2023-10-01', strategyReturn: 3.33, fundReturn: 10.0, indexReturn: 9.0 },
    { date: '2023-11-01', strategyReturn: 8.53, fundReturn: 15.5, indexReturn: 12.3 },
    { date: '2023-12-01', strategyReturn: 15.5, fundReturn: 22.9, indexReturn: 18.7 }
  ]
};

export default function PerformanceComparisonTest() {
  const [showChart, setShowChart] = useState(false);

  return (
    <div className="max-w-6xl mx-auto p-6 space-y-6">
      <div className="bg-white rounded-lg shadow-md p-6">
        <h2 className="text-2xl font-bold text-gray-900 mb-6">收益率对比图表测试</h2>
        
        <div className="mb-6">
          <p className="text-gray-600 mb-4">
            这是一个收益率对比图表的测试页面，展示了策略收益率、基金收益率和指数收益率的对比分析功能。
          </p>
          
          <div className="bg-blue-50 p-4 rounded-lg mb-4">
            <h3 className="text-lg font-semibold text-blue-900 mb-2">测试数据说明</h3>
            <ul className="text-sm text-blue-800 space-y-1">
              <li>• 测试期间：2023年1月 - 2023年12月</li>
              <li>• 投资策略：定投策略（每月5000元）</li>
              <li>• 策略最终收益率：15.5%</li>
              <li>• 基金最终收益率：22.9%</li>
              <li>• 指数最终收益率：18.7%</li>
            </ul>
          </div>
          
          <button
            onClick={() => setShowChart(!showChart)}
            className="px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
          >
            {showChart ? '隐藏图表' : '显示收益率对比图表'}
          </button>
        </div>

        {showChart && (
          <div className="space-y-6">
            <PerformanceComparisonChart result={mockBacktestResult} />
            
            <div className="bg-gray-50 p-4 rounded-lg">
              <h3 className="text-lg font-semibold text-gray-900 mb-3">图表功能测试</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                <div>
                  <h4 className="font-medium text-gray-800 mb-2">交互功能</h4>
                  <ul className="text-gray-600 space-y-1">
                    <li>✓ 悬停显示具体数值</li>
                    <li>✓ 响应式设计</li>
                    <li>✓ 零轴参考线</li>
                    <li>✓ 图例说明</li>
                  </ul>
                </div>
                <div>
                  <h4 className="font-medium text-gray-800 mb-2">数据展示</h4>
                  <ul className="text-gray-600 space-y-1">
                    <li>✓ 三线收益率对比</li>
                    <li>✓ 超额收益分析</li>
                    <li>✓ 最终收益率统计</li>
                    <li>✓ 颜色编码指示</li>
                  </ul>
                </div>
              </div>
            </div>
            
            <div className="bg-yellow-50 p-4 rounded-lg">
              <h3 className="text-lg font-semibold text-yellow-900 mb-2">分析结果</h3>
              <div className="text-sm text-yellow-800 space-y-2">
                <p>
                  <strong>策略表现：</strong>在这个测试案例中，定投策略的收益率为15.5%，
                  低于基金的买入持有收益率22.9%，但高于指数收益率18.7%。
                </p>
                <p>
                  <strong>超额收益：</strong>相对基金为-7.4%，相对指数为-3.2%。
                  这表明在这个特定时期，简单的买入持有策略表现更好。
                </p>
                <p>
                  <strong>策略优化：</strong>可以考虑调整定投频率、金额或结合其他指标来改善策略表现。
                </p>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
