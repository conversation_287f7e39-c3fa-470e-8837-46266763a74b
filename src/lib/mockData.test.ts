import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import {
  mockFunds,
  mockIndices,
  generateMockFundData,
  generateMockIndexData,
  getFundData,
  getIndexData,
  getFundById,
  getIndexById,
} from '@/lib/mockData';
import { fetchFundData } from '@/lib/api/fundApi';
import { APP_CONFIG } from '@/lib/config';

// Mock API functions
vi.mock('@/lib/api/fundApi');
vi.mock('@/lib/config');

describe('MockData', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('mockFunds', () => {
    it('应该包含测试基金数据', () => {
      expect(mockFunds).toBeDefined();
      expect(Array.isArray(mockFunds)).toBe(true);
      expect(mockFunds.length).toBeGreaterThan(0);

      // 验证测试基金
      const testFund = mockFunds.find(f => f.id === 'fund_001');
      expect(testFund).toBeDefined();
      expect(testFund?.name).toBe('测试股票基金');
      expect(testFund?.code).toBe('000001');
      expect(testFund?.type).toBe('stock');
    });

    it('应该包含真实基金数据', () => {
      const realFund = mockFunds.find(f => f.code === '022919');
      expect(realFund).toBeDefined();
      expect(realFund?.name).toContain('申万菱信');
      expect(realFund?.type).toBe('index');
      expect(realFund?.indexId).toBe('index_zz500');
    });

    it('应该包含必要的基金字段', () => {
      mockFunds.forEach(fund => {
        expect(fund.id).toBeDefined();
        expect(fund.name).toBeDefined();
        expect(fund.code).toBeDefined();
        expect(fund.type).toBeDefined();
        expect(typeof fund.id).toBe('string');
        expect(typeof fund.name).toBe('string');
        expect(typeof fund.code).toBe('string');
        expect(typeof fund.type).toBe('string');
      });
    });
  });

  describe('mockIndices', () => {
    it('应该包含指数数据', () => {
      expect(mockIndices).toBeDefined();
      expect(Array.isArray(mockIndices)).toBe(true);
      expect(mockIndices.length).toBeGreaterThan(0);

      // 验证沪深300指数
      const hs300 = mockIndices.find(i => i.code === '000300');
      expect(hs300).toBeDefined();
      expect(hs300?.name).toBe('沪深300');
      expect(hs300?.id).toBe('index_hs300');
    });

    it('应该包含必要的指数字段', () => {
      mockIndices.forEach(index => {
        expect(index.id).toBeDefined();
        expect(index.name).toBeDefined();
        expect(index.code).toBeDefined();
        expect(typeof index.id).toBe('string');
        expect(typeof index.name).toBe('string');
        expect(typeof index.code).toBe('string');
      });
    });

    it('应该包含基金引用的所有指数', () => {
      const referencedIndexIds = mockFunds
        .filter(f => f.indexId)
        .map(f => f.indexId);

      referencedIndexIds.forEach(indexId => {
        const index = mockIndices.find(i => i.id === indexId);
        expect(index).toBeDefined();
      });
    });
  });

  describe('generateMockFundData', () => {
    it('应该生成基金历史数据', () => {
      const data = generateMockFundData('fund_001', '2024-01-01', '2024-01-10');
      
      expect(Array.isArray(data)).toBe(true);
      expect(data.length).toBeGreaterThan(0);
      
      // 验证数据结构
      data.forEach(item => {
        expect(item.date).toBeDefined();
        expect(item.netAssetValue).toBeDefined();
        expect(item.accumulatedValue).toBeDefined();
        expect(typeof item.date).toBe('string');
        expect(typeof item.netAssetValue).toBe('number');
        expect(typeof item.accumulatedValue).toBe('number');
        expect(item.netAssetValue).toBeGreaterThan(0);
        expect(item.accumulatedValue).toBeGreaterThan(0);
      });
    });

    it('应该按日期排序', () => {
      const data = generateMockFundData('fund_001', '2024-01-01', '2024-01-10');
      
      for (let i = 1; i < data.length; i++) {
        expect(new Date(data[i].date) >= new Date(data[i-1].date)).toBe(true);
      }
    });

    it('应该跳过周末', () => {
      const data = generateMockFundData('fund_001', '2024-01-01', '2024-01-07');
      
      data.forEach(item => {
        const date = new Date(item.date);
        const dayOfWeek = date.getDay();
        expect(dayOfWeek).not.toBe(0); // 不是周日
        expect(dayOfWeek).not.toBe(6); // 不是周六
      });
    });

    it('应该根据基金类型设置不同的波动性', () => {
      const stockData = generateMockFundData('fund_001', '2024-01-01', '2024-01-31');
      const bondData = generateMockFundData('fund_002', '2024-01-01', '2024-01-31');
      
      // 股票基金的波动性应该大于债券基金
      const stockVolatility = calculateVolatility(stockData);
      const bondVolatility = calculateVolatility(bondData);
      
      expect(stockVolatility).toBeGreaterThan(bondVolatility);
    });

    it('应该处理单日数据', () => {
      const data = generateMockFundData('fund_001', '2024-01-01', '2024-01-01');
      
      expect(data.length).toBe(1);
      expect(data[0].date).toBe('2024-01-01');
    });

    it('应该处理跨年数据', () => {
      const data = generateMockFundData('fund_001', '2023-12-30', '2024-01-02');
      
      expect(data.length).toBeGreaterThan(0);
      expect(data[0].date).toBe('2023-12-30');
      expect(data[data.length - 1].date).toBe('2024-01-02');
    });
  });

  describe('generateMockIndexData', () => {
    it('应该生成指数历史数据', () => {
      const data = generateMockIndexData('index_hs300', '2024-01-01', '2024-01-10');
      
      expect(Array.isArray(data)).toBe(true);
      expect(data.length).toBeGreaterThan(0);
      
      // 验证数据结构
      data.forEach(item => {
        expect(item.date).toBeDefined();
        expect(item.value).toBeDefined();
        expect(item.pe).toBeDefined();
        expect(item.pb).toBeDefined();
        expect(typeof item.date).toBe('string');
        expect(typeof item.value).toBe('number');
        expect(typeof item.pe).toBe('number');
        expect(typeof item.pb).toBe('number');
        expect(item.value).toBeGreaterThan(0);
        expect(item.pe).toBeGreaterThan(0);
        expect(item.pb).toBeGreaterThan(0);
      });
    });

    it('应该根据指数设置不同的基础值', () => {
      const hs300Data = generateMockIndexData('index_hs300', '2024-01-01', '2024-01-01');
      const zz500Data = generateMockIndexData('index_zz500', '2024-01-01', '2024-01-01');
      
      expect(hs300Data[0].value).not.toBe(zz500Data[0].value);
      expect(hs300Data[0].pe).not.toBe(zz500Data[0].pe);
    });

    it('应该保持PE和PB在合理范围内', () => {
      const data = generateMockIndexData('index_hs300', '2024-01-01', '2024-01-31');
      
      data.forEach(item => {
        expect(item.pe).toBeGreaterThanOrEqual(5);
        expect(item.pe).toBeLessThanOrEqual(100);
        expect(item.pb).toBeGreaterThanOrEqual(0.5);
        expect(item.pb).toBeLessThanOrEqual(10);
      });
    });
  });

  describe('getFundData', () => {
    beforeEach(() => {
      (APP_CONFIG as any) = {
        dataSource: {
          useMockData: true
        }
      };
    });

    it('应该在模拟模式下返回模拟数据', async () => {
      const data = await getFundData('fund_001', '2024-01-01', '2024-01-10');
      
      expect(Array.isArray(data)).toBe(true);
      expect(data.length).toBeGreaterThan(0);
    });

    it('应该在真实模式下调用API', async () => {
      (APP_CONFIG as any).dataSource.useMockData = false;
      const mockApiData = [
        { date: '2024-01-01', netAssetValue: 1.0000, accumulatedValue: 1.0000 }
      ];
      (fetchFundData as any).mockResolvedValue(mockApiData);

      const data = await getFundData('000628', '2024-01-01', '2024-01-10');
      
      expect(fetchFundData).toHaveBeenCalledWith('000628', '2024-01-01', '2024-01-10');
      expect(data).toEqual(mockApiData);
    });

    it('应该处理基金ID到代码的转换', async () => {
      (APP_CONFIG as any).dataSource.useMockData = false;
      const mockApiData = [
        { date: '2024-01-01', netAssetValue: 1.0000, accumulatedValue: 1.0000 }
      ];
      (fetchFundData as any).mockResolvedValue(mockApiData);

      await getFundData('fund_001', '2024-01-01', '2024-01-10');
      
      expect(fetchFundData).toHaveBeenCalledWith('000001', '2024-01-01', '2024-01-10');
    });

    it('应该在API失败时回退到模拟数据', async () => {
      (APP_CONFIG as any).dataSource.useMockData = false;
      (fetchFundData as any).mockRejectedValue(new Error('API失败'));

      const data = await getFundData('000628', '2024-01-01', '2024-01-10');
      
      expect(Array.isArray(data)).toBe(true);
      expect(data.length).toBeGreaterThan(0);
    });

    it('应该处理无效的基金ID', async () => {
      (APP_CONFIG as any).dataSource.useMockData = false;

      await expect(getFundData('invalid_fund', '2024-01-01', '2024-01-10'))
        .rejects.toThrow('未找到基金ID: invalid_fund');
    });
  });

  describe('getFundById', () => {
    it('应该根据ID查找基金', () => {
      const fund = getFundById('fund_001');
      expect(fund).toBeDefined();
      expect(fund?.id).toBe('fund_001');
      expect(fund?.name).toBe('测试股票基金');
    });

    it('应该返回undefined当基金不存在时', () => {
      const fund = getFundById('nonexistent');
      expect(fund).toBeUndefined();
    });
  });

  describe('getIndexById', () => {
    it('应该根据ID查找指数', () => {
      const index = getIndexById('index_hs300');
      expect(index).toBeDefined();
      expect(index?.id).toBe('index_hs300');
      expect(index?.name).toBe('沪深300');
    });

    it('应该返回undefined当指数不存在时', () => {
      const index = getIndexById('nonexistent');
      expect(index).toBeUndefined();
    });
  });
});

// 辅助函数：计算波动性
function calculateVolatility(data: Array<{ netAssetValue: number }>): number {
  if (data.length < 2) return 0;
  
  const returns = [];
  for (let i = 1; i < data.length; i++) {
    const returnRate = (data[i].netAssetValue - data[i-1].netAssetValue) / data[i-1].netAssetValue;
    returns.push(returnRate);
  }
  
  const mean = returns.reduce((sum, r) => sum + r, 0) / returns.length;
  const variance = returns.reduce((sum, r) => sum + Math.pow(r - mean, 2), 0) / returns.length;
  
  return Math.sqrt(variance);
}
